import 'dart:io';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/restaurant_detail_controller.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/routes/paths.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/widgets/reduction_step_widget.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';

/// 购物车底部栏组件
/// 参考微信小程序 shoppingCart/card 组件
class RestaurantBottomBarWidget extends ConsumerWidget {
  /// 购物车图标全局Key
  final GlobalKey? cartIconKey;

  /// 关闭弹窗回调（用于在FoodDetailWidget中关闭弹窗）
  final VoidCallback? onCloseModal;

  final int? restaurantId;

  /// 构造函数
  const RestaurantBottomBarWidget({
    super.key,
    this.cartIconKey,
    this.onCloseModal,
    this.restaurantId,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 获取所需状态数据
    final isLoading = ref.watch(
      restaurantDetailControllerProvider
          .select((final state) => state.isLoading),
    );

    final isShowFoodsList = ref.watch(
      restaurantDetailControllerProvider
          .select((final state) => state.isShowFoodsList),
    );

    final foodsData = ref.watch(
      restaurantDetailControllerProvider
          .select((final state) => state.foodsData),
    );

    // 监听购物车变化
    ref.watch(shoppingCartProvider);

    //如果数据加载中或不存在，返回空组件
    if (isLoading || foodsData == null) return const SizedBox.shrink();

    // 获取购物车相关数据
    final cartNotifier = ref.read(shoppingCartProvider.notifier);

    // 总金额（实付金额 = 小计 - 满减金额 + 配送费 + 餐盒费）
    final subtotal = cartNotifier.getSubtotal(
      foodsData: foodsData,
      deliveryMode: 0,
    );

    final reductionDiscount = cartNotifier.getReductionDiscount(
      foodsData: foodsData,
      deliveryMode: 0,
    );

    final shipmentFee = cartNotifier.getShipmentFee(
      foodsData: foodsData,
      deliveryMode: 0,
    );

    final lunchBoxFee = cartNotifier.getLunchBoxFee(
      foodsData: foodsData,
      deliveryMode: 0,
    );

    // 配送费减免信息
    final shipmentValues = cartNotifier.getShipmentValues();

    // 标记是否有配送费减免
    final hasShipmentReduction =
        foodsData?.market?.hasShipmentReduction ?? false;

    // 配送费减免状态和金额
    final isShipmentReduceFee = shipmentValues?['isCurrentTierMet'] ?? false;
    final remainingAmount =
        (shipmentValues?['remainingAmount'] ?? 0).toDouble();
    final discount = (shipmentValues?['discount'] ??
            shipmentValues?['currentTierDiscount'] ??
            0)
        .toDouble();

    // 当前阶梯价格（满额条件）
    final currentTierPrice =
        (shipmentValues?['currentTierPrice'] ?? 0).toDouble();

    // 计算isClickReduceBtn逻辑（与微信小程序一致）
    // 微信小程序逻辑：isClickReduceBtn = min_delivery_price != surplus
    final bool isClickReduceBtn = currentTierPrice != remainingAmount;

    // 优惠后的配送费
    final actualShipmentFee = cartNotifier.getActualShipmentFee(
      foodsData: foodsData,
      deliveryMode: 0,
    );

    // 配送费优惠金额
    final shipmentDiscount = cartNotifier.getShipmentDiscount(
      foodsData: foodsData,
      deliveryMode: 0,
    );

    // 计算总金额, 实付金额 = 小计(food的price的合计) - 满减金额，其他的餐盒费和运费不计入
    final totalPrice =
        (double.parse(subtotal) - double.parse(reductionDiscount))
            .toStringAsFixed(2);

    // 商品数量
    final totalCount = cartNotifier.getTotalCount();

    // 原始价格（未优惠的总价 = 原始小计 + 配送费 + 餐盒费）
    // 使用普通的getSubtotal代替getCartOldSubtotal，因为后者不存在

    // 原始价格 = 原始小计(配送费+餐盒费) - 配送费 - 餐盒费
    final oldTotalPrice = (cartNotifier
            .calculateCart(
              foodsData: foodsData,
              deliveryMode: 0,
            )
            .totalFoodOriginalPrice)
        .toStringAsFixed(2);

    // 餐厅信息
    final restaurantHomeData =
        ref.read(restaurantDetailControllerProvider).restaurantData;
    final isResting = restaurantHomeData?.isResting ?? false;

    // 抽奖信息
    final bool isLottery = (foodsData?.lotteryActive ?? 0) > 0;
    final double lotteryTipsPrice =
        (foodsData?.lotteryMinOrderPrice ?? 0).toDouble();

    // 是否自取
    final bool canSelf =
        foodsData?.canSelfTake == 1 && foodsData?.canMulazimTake == 0;

    // 根据当前语言确定文本方向
    final isUg = Localizations.localeOf(context).languageCode == 'en';

    // 获取配送费优惠提示相关数据
    final profitValues = cartNotifier.getProfitValues();
    final profitStatus = profitValues?['profitStatus'] ?? false;
    final profitFee = (profitValues?['profitFee'] ?? 0).toDouble();
    final distributionParam =
        (profitValues?['distributionParam'] ?? 0).toDouble();

    // 构建UI
    return SafeArea(
      top: false,
      left: false,
      right: false,
      bottom: false,
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!isResting) ...[
              // 运费减免提示（统一处理，与微信小程序逻辑一致）
              if (hasShipmentReduction)
                _buildShipmentReductionInfo(
                  context,
                  isUg,
                  isShipmentReduceFee: isShipmentReduceFee,
                  isClickReduceBtn: isClickReduceBtn,
                  surplus: remainingAmount, // 直接使用动态计算的remainingAmount
                  currentBalance: discount,
                  profit: double.parse(actualShipmentFee),
                ),

              // 配送费提示
              if (profitStatus && isShowFoodsList)
                _buildProfitFeeInfo(
                  context,
                  isUg,
                  profitFee: profitFee,
                  distribution: distributionParam,
                ),

              // 自取提示
              if (canSelf) _buildSelfTakeInfo(context, isUg),
            ],
            // 购物车与结算（前景层）
            Container(
              child: isResting
                  ? _buildRestingContainer(context)
                  : _buildCartAndSubmit(
                      context,
                      isShowFoodsList,
                      totalCount,
                      double.parse(totalPrice),
                      double.parse(oldTotalPrice),
                      double.parse(shipmentFee),
                      double.parse(actualShipmentFee),
                      shipmentDiscount != "0" ? double.parse(shipmentFee) : 0,
                      double.parse(lunchBoxFee),
                      ref,
                    ),
            ),
            if (!isResting) ...[
              // 满减步骤
              if (cartNotifier.hasReductionActivity())
                _buildReductionStep(context, cartNotifier, foodsData),
              if (isLottery && isShowFoodsList)
                _buildLotteryTips(
                  context,
                  isUg,
                  lotteryTipsPrice: lotteryTipsPrice,
                ),
              if (Platform.isIOS)
                SizedBox(
                  height: 6.h,
                ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建配送费提示
  Widget _buildProfitFeeInfo(
    final BuildContext context,
    final bool isUg, {
    required final double profitFee,
    required final double distribution,
  }) {
    return Container(
      width: 1.sw,
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.h),
      margin: EdgeInsets.symmetric(horizontal: 35.w),
      decoration: BoxDecoration(
        color: const Color(0xFFF2FFEC),
        border: Border(
          top: BorderSide(color: AppColors.primary, width: 1.w),
          left: BorderSide(color: AppColors.primary, width: 1.w),
          right: BorderSide(color: AppColors.primary, width: 1.w),
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10.r),
          topRight: Radius.circular(10.r),
        ),
      ),
      alignment: Alignment.center,
      child: isUg
          ? Text.rich(
              TextSpan(
                children: [
                  const TextSpan(text: 'يەنە '),
                  TextSpan(
                    text: FormatUtil.formatPrice(profitFee),
                    style: TextStyle(color: Colors.red, fontSize: 17.sp),
                  ),
                  const TextSpan(text: ' يۈەنلىك سېتىۋالسىڭىز كىرا پەقەت '),
                  TextSpan(
                    text: FormatUtil.formatPrice(distribution),
                    style: TextStyle(color: Colors.red, fontSize: 17.sp),
                  ),
                  const TextSpan(text: ' يۈەن'),
                ],
              ),
              style: TextStyle(
                fontSize: 15.sp,
                color: AppColors.primary,
              ),
              textDirection: TextDirection.rtl,
            )
          : Text.rich(
              TextSpan(
                children: [
                  const TextSpan(text: '再买'),
                  TextSpan(
                    text: FormatUtil.formatPrice(profitFee),
                    style: TextStyle(color: Colors.red, fontSize: 17.sp),
                  ),
                  const TextSpan(text: '元配送费只需'),
                  TextSpan(
                    text: FormatUtil.formatPrice(distribution),
                    style: TextStyle(color: Colors.red, fontSize: 17.sp),
                  ),
                  const TextSpan(text: '元'),
                ],
              ),
              style: TextStyle(
                fontSize: 15.sp,
                color: AppColors.primary,
              ),
            ),
    );
  }

  /// 构建抽奖提示
  Widget _buildLotteryTips(
    final BuildContext context,
    final bool isUg, {
    required final double lotteryTipsPrice,
  }) {
    return Container(
      width: 1.sw,
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.h),
      margin: EdgeInsets.symmetric(horizontal: 40.w),
      decoration: BoxDecoration(
        color: const Color(0xFFFFD0D5),
        border: Border(
          left: BorderSide(color: const Color(0xFFFFDADA), width: 1.w),
          right: BorderSide(color: const Color(0xFFFFDADA), width: 1.w),
        ),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(10.r),
          bottomRight: Radius.circular(10.r),
        ),
      ),
      alignment: Alignment.center,
      child: isUg
          ? Text.rich(
              TextSpan(
                children: [
                  const TextSpan(text: 'زاكاز سوممىسى'),
                  TextSpan(
                    text: FormatUtil.formatPrice(lotteryTipsPrice),
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                      fontSize: 17.sp,
                    ),
                  ),
                  const TextSpan(
                    text:
                        'يۈەنگە يەتسە بىر قېتىم ھەقسىز چەك تارتىش پۇرسىتىگە ئېرىشەلەيسىز!',
                  ),
                ],
              ),
              style: TextStyle(
                fontSize: 15.sp,
                color: const Color(0xFFD32323),
              ),
              textDirection: TextDirection.rtl,
              textAlign: TextAlign.center,
            )
          : Text.rich(
              TextSpan(
                children: [
                  const TextSpan(text: '订单总额满'),
                  TextSpan(
                    text: FormatUtil.formatPrice(lotteryTipsPrice),
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                      fontSize: 17.sp,
                    ),
                  ),
                  const TextSpan(text: '元就能获得一次免费抽奖机会!'),
                ],
              ),
              style: TextStyle(
                fontSize: 15.sp,
                color: const Color(0xFFD32323),
              ),
              textAlign: TextAlign.center,
            ),
    );
  }

  /// 构建运费减免信息
  Widget _buildShipmentReductionInfo(
    final BuildContext context,
    final bool isUg, {
    required final bool isShipmentReduceFee,
    required final bool isClickReduceBtn,
    required final double surplus,
    required final double currentBalance,
    required final double profit,
  }) {
    return Container(
      width: 1.sw,
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.h),
      margin: EdgeInsets.symmetric(horizontal: 40.w),
      decoration: BoxDecoration(
        color: const Color(0xFFF2FFEC),
        border: Border(
          top: BorderSide(color: AppColors.primary, width: 1.w),
          left: BorderSide(color: AppColors.primary, width: 1.w),
          right: BorderSide(color: AppColors.primary, width: 1.w),
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10.r),
          topRight: Radius.circular(10.r),
        ),
      ),
      alignment: Alignment.center,
      child: isUg
          ? isShipmentReduceFee
              ? Text.rich(
                  TextSpan(
                    children: [
                      const TextSpan(text: 'ئېتىباردىن كېيىنكى كىرا '),
                      TextSpan(
                        text: FormatUtil.formatPrice(profit),
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                          fontSize: 15.sp,
                        ),
                      ),
                      const TextSpan(text: 'يۈەن'),
                    ],
                  ),
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: AppColors.primary,
                  ),
                  textDirection: TextDirection.rtl,
                  textAlign: TextAlign.center,
                )
              : Text.rich(
                  TextSpan(
                    children: [
                      // 根据isClickReduceBtn显示"يەنە"或其他文案（与微信小程序逻辑一致）
                      TextSpan(text: isClickReduceBtn ? 'يەنە ' : ''),
                      TextSpan(
                        text: FormatUtil.formatPrice(surplus),
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                          fontSize: 15.sp,
                        ),
                      ),
                      const TextSpan(text: ' يۈەنلىك سېتىۋالسىڭىز '),
                      TextSpan(
                        text: FormatUtil.formatPrice(currentBalance),
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                          fontSize: 15.sp,
                        ),
                      ),
                      const TextSpan(text: ' يۈەن كىرا ئېتىبار قىلىنىدۇ'),
                    ],
                  ),
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: AppColors.primary,
                  ),
                  textDirection: TextDirection.rtl,
                  textAlign: TextAlign.center,
                )
          : isShipmentReduceFee
              ? Text.rich(
                  TextSpan(
                    children: [
                      const TextSpan(text: '减免后的应付配送费'),
                      TextSpan(
                        text: FormatUtil.formatPrice(profit),
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                          fontSize: 15.sp,
                        ),
                      ),
                      const TextSpan(text: '元'),
                    ],
                  ),
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: AppColors.primary,
                  ),
                  textAlign: TextAlign.center,
                )
              : Text.rich(
                  TextSpan(
                    children: [
                      // 根据isClickReduceBtn显示"再买"或"满"（与微信小程序逻辑一致）
                      TextSpan(text: isClickReduceBtn ? '再买' : '满'),
                      TextSpan(
                        text: FormatUtil.formatPrice(surplus),
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                          fontSize: 15.sp,
                        ),
                      ),
                      const TextSpan(text: '元立减'),
                      TextSpan(
                        text: FormatUtil.formatPrice(currentBalance),
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                          fontSize: 15.sp,
                        ),
                      ),
                      const TextSpan(text: '元配送费'),
                    ],
                  ),
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: AppColors.primary,
                  ),
                  textAlign: TextAlign.center,
                ),
    );
  }

  /// 构建自取信息提示
  Widget _buildSelfTakeInfo(final BuildContext context, final bool isUg) {
    return Container(
      width: 1.sw,
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.h),
      margin: EdgeInsets.symmetric(horizontal: 30.w),
      decoration: BoxDecoration(
        color: const Color(0xFFF2FFEC),
        border: Border(
          top: BorderSide(color: AppColors.primary, width: 1.w),
          left: BorderSide(color: AppColors.primary, width: 1.w),
          right: BorderSide(color: AppColors.primary, width: 1.w),
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10.r),
          topRight: Radius.circular(10.r),
        ),
      ),
      alignment: Alignment.center,
      child: Text(
        isUg ? 'ئۆزى ئېلىپ كېتىش' : S.current.self_take,
        style: TextStyle(
          fontSize: 15.sp,
          color: AppColors.primary,
        ),
        textDirection: isUg ? TextDirection.rtl : TextDirection.ltr,
      ),
    );
  }

  /// 构建休息状态容器
  Widget _buildRestingContainer(final BuildContext context) {
    return Container(
      height: 60.h,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(100),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.r),
          topRight: Radius.circular(15.r),
        ),
        backgroundBlendMode: BlendMode.srcOver,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.r),
          topRight: Radius.circular(15.r),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
          child: Center(
            child: Text(
              S.current.restaurant_is_resting,
              style: TextStyle(
                color: Colors.white,
                fontSize: 19.sp,
              ),
              textDirection:
                  Localizations.localeOf(context).languageCode == 'en'
                      ? TextDirection.rtl
                      : TextDirection.ltr,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建购物车和提交按钮
  Widget _buildCartAndSubmit(
    final BuildContext context,
    final bool isShowFoodsList,
    final int totalCount,
    final double price,
    final double oldPrice,
    final double shipment,
    final double actualShipment,
    final double reduceShipmentFee,
    final double lunchBoxPrice,
    final WidgetRef ref,
  ) {
    final isUg = Localizations.localeOf(context).languageCode == 'en';
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(100.w),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(80),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      height: 55.h,
      child: Row(
        children: [
          // 购物车图标、价格和配送费部分
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFF333333),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(100.r),
                  bottomLeft: Radius.circular(100.r),
                ),
              ),
              padding: EdgeInsets.symmetric(horizontal: 2.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // 购物车图标和数量
                  GestureDetector(
                    onTap: totalCount > 0
                        ? () {
                            // 如果在弹窗中（有关闭回调），先关闭弹窗
                            if (onCloseModal != null) {
                              onCloseModal!();
                            }

                            // 更新购物车显示状态
                            final controller = ref.read(
                              restaurantDetailControllerProvider.notifier,
                            );
                            if (isShowFoodsList) {
                              controller.hideFoodsList();
                            } else {
                              controller.showFoodsList();
                            }
                          }
                        : null,
                    child: Container(
                      width: 60.w,
                      height: 45.h,
                      margin: EdgeInsets.only(right: 10.w),
                      child: Stack(
                        alignment: Alignment.centerRight,
                        children: [
                          // 购物车背景
                          if (totalCount > 0)
                            Container(
                              width: 60.w,
                              height: 45.h,
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  begin: Alignment.bottomRight,
                                  end: Alignment.topLeft,
                                  colors: [
                                    Color(0xFF333333),
                                    Colors.white,
                                  ],
                                  stops: [0.15, 1.37],
                                ),
                              ),
                            ),

                          // 购物车图标
                          Image.asset(
                            'assets/images/card.png',
                            key: cartIconKey,
                            width: 50.w,
                            height: 45.h,
                            color: totalCount > 0 ? null : Colors.grey,
                          ),

                          // 购物车数量
                          Positioned(
                            top: 0,
                            right: 0,
                            child: Container(
                              width: 23.w,
                              height: 23.w,
                              decoration: BoxDecoration(
                                color: totalCount > 0
                                    ? Color(0xFFF62C2C)
                                    : Colors.grey,
                                shape: BoxShape.circle,
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                '$totalCount',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // 价格信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 价格显示
                        Row(
                          children: [
                            Text(
                              '¥${FormatUtil.formatPrice(price)}',
                              style: TextStyle(
                                fontSize: 20.sp,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(width: 10.w),
                            if (oldPrice != price && oldPrice > 0)
                              Text(
                                '¥${FormatUtil.formatPrice(oldPrice)}',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: Colors.grey,
                                  decoration: TextDecoration.lineThrough,
                                  decorationColor: Colors.white,
                                ),
                              ),
                          ],
                        ),

                        // 配送费信息
                        Row(
                          textDirection: TextDirection.ltr,
                          children: [
                            if (isUg) ...[
                              // 维语版布局
                              if (reduceShipmentFee > 0 &&
                                  actualShipment < reduceShipmentFee)
                                Text(
                                  '¥${FormatUtil.formatPrice(reduceShipmentFee)}',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: Colors.grey,
                                    decoration: TextDecoration.lineThrough,
                                    decorationColor: Colors.white,
                                  ),
                                ),
                              if (reduceShipmentFee > 0 &&
                                  actualShipment < reduceShipmentFee)
                                SizedBox(width: 5.w),
                              Text(
                                '¥${FormatUtil.formatPrice(actualShipment)}',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: Colors.white,
                                ),
                              ),
                              SizedBox(width: 5.w),
                              Text(
                                ': كىرا',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: Colors.grey,
                                ),
                              ),
                            ] else ...[
                              // 中文版布局
                              Text(
                                '配送费:',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: Colors.grey,
                                ),
                              ),
                              SizedBox(width: 5.w),
                              if (reduceShipmentFee > 0 &&
                                  actualShipment < reduceShipmentFee)
                                Text(
                                  '¥${FormatUtil.formatPrice(reduceShipmentFee)}',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: Colors.grey,
                                    decoration: TextDecoration.lineThrough,
                                    decorationColor: Colors.white,
                                  ),
                                ),
                              if (reduceShipmentFee > 0 &&
                                  actualShipment < reduceShipmentFee)
                                Text(
                                  '/',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: Colors.grey,
                                  ),
                                ),
                              Text(
                                '¥${FormatUtil.formatPrice(actualShipment)}',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 提交按钮
          GestureDetector(
            onTap: totalCount > 0
                ? () {
                    final buildingId =
                        ref.read(homeNoticeProvider).value?.location?.id ?? 0;
                    context.push(
                      AppPaths.submitOrderPage,
                      extra: {
                        'restaurantId': ref.read(
                          restaurantDetailControllerProvider.select(
                            (final state) =>
                                state.restaurantData?.id ?? (restaurantId ?? 0),
                          ),
                        ),
                        'buildingId': buildingId,
                        'homeBuildingId': buildingId,
                      },
                    );
                  }
                : null,
            child: Container(
              width: 110.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: totalCount > 0 ? const Color(0xFF4CAF50) : Colors.black,
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(100.r),
                  bottomRight: Radius.circular(100.r),
                ),
              ),
              alignment: Alignment.center,
              child: Text(
                isUg ? 'تامام' : S.current.cart_confirm,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建满减步骤
  Widget _buildReductionStep(
    final BuildContext context,
    final ShoppingCart cartNotifier,
    final FoodsData? foodsData,
  ) {
    // 获取满减数据
    final reductionValues = cartNotifier.getReductionValues();
    if (reductionValues == null ||
        foodsData?.market?.steps?.reductionStep == null) {
      return const SizedBox.shrink();
    }

    final reductionStepList = foodsData!.market!.steps!.reductionStep!;
    if (reductionStepList.isEmpty) {
      return const SizedBox.shrink();
    }

    // 转换步骤列表为Map格式
    final stepList = reductionStepList
        .map(
          (step) => {
            'price': step.price ?? 0,
            'reduce': step.reduce ?? 0,
          },
        )
        .toList();

    // 计算状态和参数
    final isCurrentTierMet = reductionValues['isCurrentTierMet'] ?? false;
    final hasNextTier = reductionValues['hasNextTier'] ?? false;
    final allTiersCompleted = reductionValues['allTiersCompleted'] ?? false;

    final currentDiscount =
        (reductionValues['currentDiscount'] ?? 0).toDouble();
    final remainingAmount =
        (reductionValues['remainingAmount'] ?? 0).toDouble();
    final nextDiscount = (reductionValues['nextDiscount'] ?? 0).toDouble();
    final currentTierPrice =
        (reductionValues['currentTierPrice'] ?? 0).toDouble();
    final currentTierDiscount =
        (reductionValues['currentTierDiscount'] ?? 0).toDouble();

    // 根据微信小程序逻辑计算状态
    int state = 0;
    double butSmall = 0;
    double reduce = 0;
    double futureReduce = 0;

    if (allTiersCompleted) {
      // 状态3：达到最高级
      state = 3;
      reduce = currentDiscount;
    } else if (isCurrentTierMet && hasNextTier) {
      // 状态2：达到当前级但有下一级
      state = 2;
      butSmall = remainingAmount;
      reduce = currentDiscount;
      futureReduce = nextDiscount;
    } else if (isCurrentTierMet && !hasNextTier) {
      // 状态3：达到最高级
      state = 3;
      reduce = currentDiscount;
    } else {
      // 状态1：未达到当前级
      state = 1;
      butSmall = remainingAmount > 0 ? remainingAmount : currentTierPrice;
      reduce = currentTierDiscount > 0 ? currentTierDiscount : currentDiscount;
    }

    // 活动状态 - 根据是否有活动商品确定
    // 参考微信小程序 takeFoodList.js 中的逻辑：
    // const state = Array.isArray(market?.reduction_foods_id) && market.reduction_foods_id.length ? 1 : 2;
    final reductionFoodsId = foodsData.market?.reductionFoodsId ?? [];
    final resState = reductionFoodsId.isNotEmpty ? 1 : 2; // 1: 美食满减活动，2: 餐厅满减活动

    return ReductionStepWidget(
      stepList: stepList,
      butSmall: butSmall,
      state: state,
      reduce: reduce,
      futureReduce: futureReduce,
      resState: resState,
    );
  }
}
